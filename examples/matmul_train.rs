use candle_core::{<PERSON><PERSON><PERSON>, <PERSON>ce, Tensor};
use candle_nn::{loss, Optimizer, Var<PERSON><PERSON><PERSON>, VarMap};
use candle_tests::mnist_matmul_optimum::MatmulLayer;
use image::{<PERSON>B<PERSON><PERSON>, Lu<PERSON>};



fn main() -> anyhow::Result<()> {
    println!("loading MNIST dataset...");
    let mnist = candle_datasets::vision::mnist::load()?;
    let train_images = mnist.train_images;


    let mut varmap = VarMap::new();
    let vb = VarBuilder::from_varmap(&varmap, DType::F32, &Device::Cpu);
    let var_space = Tensor::new(array, device)
    let matmul_layer = MatmulLayer::load(vb, train_images.clone())?;
    varmap = matmul_layer.init(train_images.clone(), varmap)?;

    let adamw_params = candle_nn::ParamsAdamW {
        lr: 0.1,
        weight_decay: 0.02,
        ..Default::default()
    };
    let mut optimizer = candle_nn::AdamW::new(varmap.all_vars(), adamw_params)?;


    let train_images_dims = train_images.clone().dims().to_vec();
    for epoch in 0..10000000 {
        let index = rand::random::<u32>() % train_images_dims[1] as u32;
        let image_t = train_images.clone().get_on_dim(0,index as usize)?;
        let (pred_t,probs) = matmul_layer.forward(&image_t)?;

        // create tensor identical to probs with all 0 and a 1 in the index of the highest predicted value
        let max_index = probs.squeeze(0)?.argmax(0)?;
        let max_index: Vec<u32> = max_index.to_vec1()?;
        let max_index = max_index[0];
        println!("max_index: {:?}", max_index);
        // Create one-hot encoded tensor by creating a vector and setting the max index to 1
        let mut one_hot_vec = vec![0.0f32; probs.dims()[0]];
        one_hot_vec[max_index as usize] = 1.0f32;
        let label = Tensor::from_vec(one_hot_vec, probs.shape(), probs.device())?;

        let loss = loss::mse(&probs, &label_t)?;//probs.sub(&label_t)?.powf(2.0)?.mean_all()?;
        println!("Epoch {}: Loss = {}", epoch, loss.to_scalar::<f32>()?);
        optimizer.backward_step(&loss)?;

            tensor_to_image(&pred_t, "pred_t")?;
            tensor_to_image(&image_t, "image_t")?;
        

    }

    Ok(())
}


fn tensor_to_image(tensor: &Tensor,name: &str) -> anyhow::Result<()> {
    // Reshape from [784] to [28, 28]
    let image_tensor = tensor.reshape((28, 28))?;
    let max_val = image_tensor.max_all()?;
    let image_tensor = image_tensor.broadcast_div(&max_val)?;
    
    // Convert to Vec and normalize to [0, 255]
    let data: Vec<Vec<f32>> = image_tensor.to_vec2()?;
    // Create image buffer
    let mut img_buffer = ImageBuffer::new(28, 28);
    
    for (y, row) in data.iter().enumerate() {
        for (x, &pixel) in row.iter().enumerate() {
            // Assuming pixel values are in [0, 1] range
            let pixel_u8 = (pixel.clamp(0.0, 1.0) * 255.0) as u8;
            img_buffer.put_pixel(x as u32, y as u32, Luma([pixel_u8]));
        }
    }
    
    // Save image
    img_buffer.save(format!("{name}.png"))?;
    
    Ok(())
}